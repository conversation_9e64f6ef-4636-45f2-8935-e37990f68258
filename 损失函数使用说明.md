# 股票趋势预测损失函数使用说明

## 问题背景

您的目标值是 `(第三天最高价 / 第二天开盘价 - 1) * 100`，旨在捕捉上涨的初期趋势，而不是反弹。传统的MSE损失函数可能导致模型更倾向于预测反弹而不是真正的上涨趋势。

## 解决方案

我为您设计了专门的损失函数来解决这个问题：

### 1. TrendCaptureLoss (趋势捕捉损失)

**设计思路：**
- 对于正目标值（上涨），给予更高的权重，鼓励模型准确预测上涨
- 对于预测值与目标值方向一致的情况，给予奖励
- 对于预测反弹（负目标值但预测为正）的情况，给予严厉惩罚
- 使用Huber Loss的思想，对异常值不那么敏感

**关键参数：**
- `alpha=2.0`: 上涨权重系数，越大越重视上涨预测
- `beta=1.5`: 方向一致奖励系数，越大对方向正确的奖励越多
- `gamma=3.0`: 反弹惩罚系数，越大对反弹误判的惩罚越严厉
- `trend_threshold=0.5`: 趋势判断阈值，超过此值认为是明显趋势

### 2. RankingLoss (排序损失)

专注于相对排序而非绝对值预测，适合股票选择任务。

### 3. CombinedLoss (组合损失)

结合趋势捕捉损失和排序损失，平衡绝对预测和相对排序。

## 使用方法

### 训练时指定损失函数类型

```bash
# 使用趋势捕捉损失函数（推荐）
python train.py --loss_type trend_capture --trend_alpha 2.0 --trend_beta 1.5 --trend_gamma 3.0 --trend_threshold 0.5

# 使用组合损失函数
python train.py --loss_type combined --trend_alpha 2.0 --trend_beta 1.5 --trend_gamma 3.0

# 使用排序损失函数
python train.py --loss_type ranking

# 使用传统MSE损失函数（对比用）
python train.py --loss_type mse
```

### 参数调优建议

1. **trend_alpha (上涨权重系数)**
   - 默认值：2.0
   - 如果希望更重视上涨预测，可以增加到2.5-3.0
   - 如果模型过度偏向预测上涨，可以降低到1.5-1.8

2. **trend_beta (方向一致奖励系数)**
   - 默认值：1.5
   - 如果希望更重视预测方向的正确性，可以增加到2.0
   - 如果发现模型过于保守，可以降低到1.2-1.3

3. **trend_gamma (反弹惩罚系数)**
   - 默认值：3.0
   - 这是最重要的参数，用于严厉惩罚反弹误判
   - 如果模型仍然倾向于预测反弹，可以增加到4.0-5.0
   - 如果模型变得过于保守，可以降低到2.0-2.5

4. **trend_threshold (趋势判断阈值)**
   - 默认值：0.5
   - 根据您的目标值分布调整
   - 如果目标值普遍较小，可以降低到0.3
   - 如果目标值普遍较大，可以增加到0.8-1.0

## 测试结果分析

根据测试结果，趋势捕捉损失函数表现出以下特点：

1. **对正确预测上涨给予奖励**：损失值比MSE更小
2. **对错误预测进行适度惩罚**：避免过度惩罚导致训练不稳定
3. **对反弹误判进行严厉惩罚**：这是关键特性，有助于避免预测反弹
4. **对正确预测下跌也给予奖励**：保持模型的平衡性

## 监控指标

训练时请重点关注以下指标：

1. **IC (Information Coefficient)**：信息系数，衡量预测值与真实值的相关性
2. **RIC (Rank IC)**：排序信息系数，衡量预测排序与真实排序的相关性
3. **方向准确率**：预测方向与真实方向一致的比例
4. **上涨捕捉率**：在真实上涨的情况下，预测也为上涨的比例

## 建议的训练策略

1. **先用trend_capture损失函数训练**，观察模型是否能更好地捕捉上涨趋势
2. **如果效果不理想，调整参数**：
   - 增加trend_gamma来更严厉地惩罚反弹误判
   - 增加trend_alpha来更重视上涨预测
3. **可以尝试combined损失函数**，结合趋势捕捉和排序优化
4. **对比不同损失函数的效果**，选择最适合您数据的版本

## 注意事项

1. 新的损失函数可能需要调整学习率，建议从较小的学习率开始
2. 训练初期损失值可能会比MSE高，这是正常的，因为我们引入了额外的约束
3. 重点关注验证集上的IC和RIC指标，而不仅仅是损失值
4. 建议保存多个检查点（最佳损失、最佳IC、最佳RIC），以便选择最适合的模型

通过这些改进，您的模型应该能够更好地捕捉上涨的初期趋势，而不是反弹。
