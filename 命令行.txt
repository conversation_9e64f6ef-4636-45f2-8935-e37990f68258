
#10天 隔日模型 
python trade_o.py --weight_type best_ric --save_all_predictions  --output_dir gp/cyb2 --top_n 10 --run_id v2 --start_date 2025-06-01 --end_date 2025-07-09 --data_path tushare_data_cyb/stock_factors_cyb.csv



python trade_o.py --weight_type best_ric  --output_dir gp/cyb2 --top_n 10 --run_id v2 --start_date 2025-06-01 --end_date 2025-07-09 --data_path tushare_data_cyb/stock_factors_cyb_train.csv




python trade_o.py --weight_type best_ric --output_dir gp/cyb --top_n 10 --run_id v1 --start_date 2025-06-01 --end_date 2025-07-10 --data_path tushare_data_cyb/stock_factors_cyb.csv


python trade_o.py --weight_type best --save_all_predictions --output_dir gp/cyb2 --top_n 10 --run_id v2 --start_date 2025-07-01 --end_date 2025-07-08 --data_path tushare_data_cyb/stock_factors_cyb.csv


python trade_o.py --weight_type best_ric --output_dir gp/zhuban --top_n 10 --run_id v1 --start_date 2025-06-01 --end_date 2025-07-10 --data_path tushare_data_mainboard/stock_factors_mainboard.csv


