#!/usr/bin/env python3
"""
简单的损失函数测试
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class TrendCaptureLoss(nn.Module):
    """
    专门用于捕捉上涨初期趋势的损失函数
    """
    
    def __init__(self, 
                 alpha=2.0,      # 上涨权重系数
                 beta=1.5,       # 方向一致奖励系数
                 gamma=3.0,      # 反弹惩罚系数
                 delta=1.0,      # Huber Loss的阈值
                 trend_threshold=0.5):  # 趋势判断阈值
        super(TrendCaptureLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.delta = delta
        self.trend_threshold = trend_threshold
        
    def forward(self, predictions, targets):
        """
        计算损失
        """
        # 基础Huber Loss
        diff = predictions - targets
        huber_loss = torch.where(
            torch.abs(diff) <= self.delta,
            0.5 * diff ** 2,
            self.delta * (torch.abs(diff) - 0.5 * self.delta)
        )
        
        # 1. 上涨权重：对于正目标值给予更高权重
        upward_weight = torch.where(
            targets > self.trend_threshold,
            self.alpha,
            1.0
        )
        
        # 2. 方向一致奖励：预测方向与实际方向一致时减少损失
        direction_consistency = torch.sign(predictions) * torch.sign(targets)
        direction_bonus = torch.where(
            direction_consistency > 0,
            1.0 / self.beta,  # 方向一致时减少损失
            1.0
        )
        
        # 3. 反弹惩罚：预测上涨但实际下跌的情况
        false_positive_penalty = torch.where(
            (predictions > self.trend_threshold) & (targets < -self.trend_threshold),
            self.gamma,  # 严重惩罚假阳性
            1.0
        )
        
        # 4. 趋势强度权重：目标值绝对值越大，权重越高
        trend_strength = 1.0 + torch.abs(targets) / 10.0  # 归一化趋势强度
        
        # 综合损失
        weighted_loss = huber_loss * upward_weight * direction_bonus * false_positive_penalty * trend_strength
        
        return weighted_loss.mean()

def test_loss_functions():
    """测试不同损失函数的行为"""
    
    print("=" * 60)
    print("损失函数测试结果")
    print("=" * 60)
    
    # 创建测试数据
    scenarios = [
        ("真实上涨，预测上涨", torch.tensor([2.0, 3.5, 1.8]), torch.tensor([1.8, 3.2, 1.5])),
        ("真实上涨，预测下跌", torch.tensor([2.0, 3.5, 1.8]), torch.tensor([-0.5, -1.2, -0.8])),
        ("真实下跌，预测上涨", torch.tensor([-1.5, -2.3, -0.8]), torch.tensor([1.2, 2.1, 0.9])),
        ("真实下跌，预测下跌", torch.tensor([-1.5, -2.3, -0.8]), torch.tensor([-1.2, -2.0, -0.6]))
    ]
    
    # 初始化损失函数
    mse_loss = nn.MSELoss()
    trend_loss = TrendCaptureLoss(alpha=2.0, beta=1.5, gamma=3.0, trend_threshold=0.5)
    
    for scenario_name, targets, preds in scenarios:
        print(f"\n场景: {scenario_name}")
        print(f"目标值: {targets.numpy()}")
        print(f"预测值: {preds.numpy()}")
        
        mse_val = mse_loss(preds, targets).item()
        trend_val = trend_loss(preds, targets).item()
        
        print(f"MSE损失:      {mse_val:.4f}")
        print(f"趋势捕捉损失: {trend_val:.4f}")
        print(f"损失比率:     {trend_val/mse_val:.2f}x")
        print("-" * 40)

if __name__ == "__main__":
    print("开始测试损失函数...")
    test_loss_functions()
    print("\n测试完成!")
