import tushare as ts
import pandas as pd
import os
import time
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# --- 配置信息 ---
# !! 务必替换为你的 Tushare Pro API token
TUSHARE_TOKEN = '28af7cb8a5cfd2468fdbc7649f1ed354cd2fd1ae4d7ceafe4a3cfecd'
DATA_DIR = 'tushare_data_mainboard'  # 保存CSV文件的文件夹


TARGET_START_DATE = '20240101'  # 开始日期 (YYYYMMDD)
TARGET_END_DATE = '20250710'    # 结束日期 (YYYYMMDD)

# API 调用频率限制
MAX_CALLS_PER_MINUTE = 190 # 每分钟最大调用次数
TIME_WINDOW = 60  # 时间窗口（秒）
api_call_timestamps = [] # 存储API调用的时间戳
rate_limit_lock = threading.Lock() # 线程锁，用于保护时间戳列表的访问

# 多线程配置
MAX_WORKERS = 8 # 最大线程数

# 每次成功调用API后强制等待的时间（秒）
INTER_CALL_DELAY = 0.15 # 0.1 ~ 0.3 之间尝试

# --- 定义需要获取的字段 ---
# 主板股票日线字段
STOCK_DAILY_FIELDS = 'ts_code,trade_date,pre_close,open,high,low,close,change,pct_chg,vol,amount,turnover_rate,volume_ratio,pe,pb'

# 主板股票基本信息字段
STOCK_BASIC_FIELDS = 'ts_code,symbol,name,area,industry,market,list_date,delist_date,is_hs'

# --- 文件名配置 ---
STOCK_BASIC_FILE = "stock_basic_mainboard.csv"
STOCK_DAILY_FILE = "stock_daily_mainboard.csv"
DAILY_BASIC_FILE = "daily_basic_mainboard.csv"

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Tushare 初始化 ---
try:
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
    logging.info("Tushare Pro API 初始化成功。")
except Exception as e:
    logging.error(f"初始化 Tushare Pro API 失败: {e}")
    exit()

# --- 辅助函数 ---

def ensure_dir(directory):
    """如果目录不存在，则创建目录。"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logging.info(f"已创建目录: {directory}")

def rate_limiter():
    """检查并执行API调用频率限制，必要时进行等待。"""
    with rate_limit_lock:
        now = time.time()
        # 移除时间窗口外的旧时间戳
        api_call_timestamps[:] = [t for t in api_call_timestamps if now - t < TIME_WINDOW]
        # 检查是否达到限制
        if len(api_call_timestamps) >= MAX_CALLS_PER_MINUTE:
            # 计算需要等待的时间
            time_to_wait = TIME_WINDOW - (now - api_call_timestamps[0]) + 0.1
            logging.warning(f"已达到API调用频率限制 ({MAX_CALLS_PER_MINUTE}/分钟). 等待 {time_to_wait:.2f} 秒...")
            time.sleep(time_to_wait)
            # 睡醒后再次清理一下时间戳列表，确保准确
            now = time.time()
            api_call_timestamps[:] = [t for t in api_call_timestamps if now - t < TIME_WINDOW]
        # 记录本次调用时间
        api_call_timestamps.append(now)

def determine_fetch_ranges_per_code(file_path, code_column, all_codes, target_start_dt, target_end_dt, stock_basic_df=None):
    """直接按照设置的日期范围为每个代码创建下载任务，不检查已有数据。"""
    tasks = []
    skipped_delisted_codes = [] # 已摘牌且不需要下载的代码
    skipped_not_listed_codes = [] # 在目标日期还未上市的代码

    logging.info(f"将为所有代码下载完整日期范围: {target_start_dt.strftime('%Y%m%d')} 到 {target_end_dt.strftime('%Y%m%d')}")

    # 创建代码到上市日期和摘牌日期的映射
    stock_info_dict = {}
    if stock_basic_df is not None and not stock_basic_df.empty:
        for _, row in stock_basic_df.iterrows():
            ts_code = row.get('ts_code')
            list_date = row.get('list_date')
            delist_date = row.get('delist_date')
            if ts_code and (list_date is not None or delist_date is not None):
                stock_info_dict[ts_code] = {
                    'list_date': list_date if pd.notna(list_date) else None,
                    'delist_date': delist_date if pd.notna(delist_date) else None
                }

    # 为每个代码确定下载范围
    for code in all_codes:
        start_fetch_dt = target_start_dt  # 从目标开始日期下载
        end_fetch_dt = target_end_dt  # 到目标结束日期下载

        # 检查上市日期和摘牌日期
        if stock_info_dict and code in stock_info_dict:
            stock_info = stock_info_dict[code]
            list_date_str = stock_info.get('list_date')
            delist_date_str = stock_info.get('delist_date')

            # 如果有上市日期，且上市日期晚于目标开始日期，则从上市日期开始下载
            if list_date_str and list_date_str.strip():
                try:
                    list_dt = datetime.strptime(list_date_str, '%Y%m%d')
                    if list_dt > target_start_dt:
                        start_fetch_dt = list_dt
                        logging.debug(f"代码 {code}: 上市日期 {list_date_str} 晚于目标开始日期 {target_start_dt.strftime('%Y%m%d')}，将从上市日期开始下载。")

                    # 如果上市日期晚于目标结束日期，则跳过该代码
                    if list_dt > target_end_dt:
                        logging.debug(f"代码 {code}: 上市日期 {list_date_str} 晚于目标结束日期 {target_end_dt.strftime('%Y%m%d')}，跳过下载。")
                        skipped_not_listed_codes.append(code)
                        continue
                except ValueError:
                    logging.warning(f"代码 {code}: 上市日期格式错误 '{list_date_str}'")

            # 如果有摘牌日期，且摘牌日期早于目标结束日期，则只下载到摘牌日期
            if delist_date_str and delist_date_str.strip():
                try:
                    delist_dt = datetime.strptime(delist_date_str, '%Y%m%d')
                    if delist_dt < target_end_dt:
                        end_fetch_dt = delist_dt
                        logging.debug(f"代码 {code}: 摘牌日期 {delist_date_str} 早于目标结束日期 {target_end_dt.strftime('%Y%m%d')}，将只下载到摘牌日期。")

                    # 如果摘牌日期早于目标开始日期，则跳过该代码
                    if delist_dt < target_start_dt:
                        logging.debug(f"代码 {code}: 摘牌日期 {delist_date_str} 早于目标开始日期 {target_start_dt.strftime('%Y%m%d')}，跳过下载。")
                        skipped_delisted_codes.append(code)
                        continue
                except ValueError:
                    logging.warning(f"代码 {code}: 摘牌日期格式错误 '{delist_date_str}'")

        # 如果计算出的开始日期在结束日期之后，则无需下载
        if start_fetch_dt <= end_fetch_dt:
            start_fetch_str = start_fetch_dt.strftime('%Y%m%d')
            end_fetch_str = end_fetch_dt.strftime('%Y%m%d')
            tasks.append((code, start_fetch_str, end_fetch_str))
            logging.debug(f"代码 {code}: 确定下载范围: {start_fetch_str} 到 {end_fetch_str}")

    if skipped_delisted_codes:
        logging.info(f"已跳过 {len(skipped_delisted_codes)} 个已摘牌且目标开始日期晚于摘牌日期的代码。")

    if skipped_not_listed_codes:
        logging.info(f"已跳过 {len(skipped_not_listed_codes)} 个上市日期晚于目标结束日期的代码。")

    logging.info(f"为 {os.path.basename(file_path)} 确定了 {len(tasks)} 个下载任务。")
    return tasks

def filter_stock_daily_data(df):
    """过滤主板股票日线数据：
    1. 如果open、high、low、close中任一个为0或空，则不保存该行
    2. 过滤掉成交量为0的数据（停牌等情况）
    """
    if df is None or df.empty:
        return df

    # 初始行数
    orig_len = len(df)

    # 检查价格字段不为0且不为空
    price_cols = ['open', 'high', 'low', 'close']
    price_mask = True
    for col in price_cols:
        if col in df.columns:
            price_mask = price_mask & ~df[col].isna() & (df[col] != 0)

    # 检查成交量不为0（过滤停牌数据）
    vol_mask = True
    if 'vol' in df.columns:
        vol_mask = ~df['vol'].isna() & (df['vol'] > 0)

    # 应用过滤条件
    filtered_df = df[price_mask & vol_mask].copy()

    # 计算过滤掉的行数
    filtered_rows = orig_len - len(filtered_df)
    if filtered_rows > 0:
        logging.info(f"已过滤 {filtered_rows} 行不符合条件的主板股票日线数据")

    return filtered_df

def save_data(df, file_path):
    """将DataFrame数据追加到CSV文件，保存所有列。"""
    if df is None or df.empty:
        logging.warning(f"尝试保存空数据到 {os.path.basename(file_path)}，已跳过。")
        return
    try:
        # 创建数据副本，避免修改原始数据
        df_copy = df.copy()

        file_exists = os.path.exists(file_path)
        # 确保日期列是 YYYYMMDD 字符串格式
        date_column = None
        if 'trade_date' in df_copy.columns:
             date_column = 'trade_date'
        elif 'date' in df_copy.columns: # 适用于其他接口
             date_column = 'date'

        if date_column and pd.api.types.is_datetime64_any_dtype(df_copy[date_column]):
            df_copy[date_column] = df_copy[date_column].dt.strftime('%Y%m%d')
        # 如果是股票数据，确保按代码和日期排序
        if 'ts_code' in df_copy.columns and 'trade_date' in df_copy.columns:
             df_copy = df_copy.sort_values(by=['ts_code', 'trade_date']).reset_index(drop=True)

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 保存数据
        df_copy.to_csv(file_path, mode='a', header=not file_exists, index=False, encoding='utf-8-sig')
        action = "追加了" if file_exists else "创建并保存了"
        logging.info(f"{action} {len(df_copy)} 行数据到 {os.path.basename(file_path)}")

        # 验证保存是否成功
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            logging.debug(f"文件 {os.path.basename(file_path)} 保存成功，文件大小: {file_size} 字节")
        else:
            logging.error(f"文件 {os.path.basename(file_path)} 保存后不存在！")

    except Exception as e:
        logging.error(f"保存数据到 {file_path} 失败: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def save_data_overwrite(df, file_path):
    """将DataFrame数据覆盖保存到CSV文件，保存所有列。"""
    if df is None or df.empty:
        logging.warning(f"尝试覆盖保存空数据到 {os.path.basename(file_path)}，已跳过。")
        return
    try:
        # 创建数据副本，避免修改原始数据
        df_copy = df.copy()

        # 确保日期列是 YYYYMMDD 字符串格式
        date_column = None
        if 'trade_date' in df_copy.columns:
             date_column = 'trade_date'
        elif 'date' in df_copy.columns: # 适用于其他接口
             date_column = 'date'

        if date_column and pd.api.types.is_datetime64_any_dtype(df_copy[date_column]):
            df_copy[date_column] = df_copy[date_column].dt.strftime('%Y%m%d')
        # 如果是股票数据，确保按代码和日期排序
        if 'ts_code' in df_copy.columns and 'trade_date' in df_copy.columns:
             df_copy = df_copy.sort_values(by=['ts_code', 'trade_date']).reset_index(drop=True)

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 保存数据
        df_copy.to_csv(file_path, mode='w', header=True, index=False, encoding='utf-8-sig')
        logging.info(f"已覆盖保存 {len(df_copy)} 行数据到 {os.path.basename(file_path)}")

        # 验证保存是否成功
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            logging.debug(f"文件 {os.path.basename(file_path)} 覆盖保存成功，文件大小: {file_size} 字节")
        else:
            logging.error(f"文件 {os.path.basename(file_path)} 覆盖保存后不存在！")

    except Exception as e:
        logging.error(f"覆盖保存数据到 {file_path} 失败: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def fetch_with_retry(api_func, fields=None, max_retries=3, delay=5, **kwargs):
    """调用Tushare API，带限流、延时、重试，并可指定fields。"""
    retries = 0
    # 将 fields 加入 kwargs 供 API 调用
    if fields:
        kwargs['fields'] = fields

    while retries < max_retries:
        try:
            rate_limiter()
            df = api_func(**kwargs)
            time.sleep(INTER_CALL_DELAY)
            return df if df is not None else pd.DataFrame()
        except Exception as e:
            retries += 1
            # 检查是否是 Tushare API 错误，特别是频率限制错误
            if "api daily limit reached" in str(e).lower() or "tushare.pro daily limit reached" in str(e).lower() or "quota limit reached" in str(e).lower():
                 logging.error(f"API 调用失败 ({api_func.__name__}, 尝试 {retries}/{max_retries}, 参数: {kwargs}): 达到日调用限制或配额限制！请检查您的积分和接口权限。")
                 # 如果达到日限，不再重试，直接返回空DataFrame
                 return pd.DataFrame()
            logging.warning(f"API 调用失败 ({api_func.__name__}, 尝试 {retries}/{max_retries}, 参数: {kwargs}): {e}。将在 {delay} 秒后重试...")
            if retries >= max_retries:
                logging.error(f"API 调用最终失败: {api_func.__name__} (参数: {kwargs})。错误: {e}")
                time.sleep(delay)
                return pd.DataFrame()
            time.sleep(delay)
    return pd.DataFrame()

# --- 数据获取函数 ---

def get_stock_basic_info():
    """获取主板股票基本信息并保存到 stock_basic_mainboard.csv。"""
    file_path = os.path.join(DATA_DIR, STOCK_BASIC_FILE)
    logging.info("正在获取主板股票基本信息...")
    
    # 获取主板股票列表 (market='主板')
    df = fetch_with_retry(pro.stock_basic,
                         fields=STOCK_BASIC_FIELDS,
                         market='主板')
    
    if not df.empty:
        try:
            # 基本信息文件通常是覆盖保存
            save_data_overwrite(df, file_path)
        except Exception as e:
             logging.error(f"保存主板股票基本信息到 {file_path} 失败: {e}")
             # 如果保存失败，尝试读取现有的文件，以便后续流程能拿到代码列表
             if os.path.exists(file_path): return pd.read_csv(file_path)
             else: return pd.DataFrame() # 如果文件也不存在，返回空DataFrame
        return df
    else:
        logging.warning("获取主板股票基本信息失败或无数据。")
        # 如果获取失败，尝试读取现有的文件，以便后续流程能拿到代码列表
        if os.path.exists(file_path):
             logging.info(f"尝试读取现有主板股票基本信息文件 {os.path.basename(file_path)}...")
             try:
                 return pd.read_csv(file_path)
             except Exception as read_e:
                 logging.error(f"读取现有主板股票基本信息文件 {os.path.basename(file_path)} 失败: {read_e}")
                 return pd.DataFrame()
        else:
             logging.warning(f"主板股票基本信息文件 {os.path.basename(file_path)} 不存在。")
             return pd.DataFrame()

def fetch_stock_daily_task(ts_code, start_date, end_date):
    """线程任务：获取单个主板股票日线数据，使用 STOCK_DAILY_FIELDS 指定字段。"""
    logging.debug(f"线程任务：获取 Stock_Daily {ts_code} 从 {start_date} 到 {end_date}")
    df_new = fetch_with_retry(pro.daily,
                              fields=STOCK_DAILY_FIELDS, # 指定需要的字段
                              ts_code=ts_code,
                              start_date=start_date,
                              end_date=end_date)
    if not df_new.empty:
        logging.debug(f"API返回 {len(df_new)} 条 {ts_code} 的原始数据")
        # 过滤数据
        df_new = filter_stock_daily_data(df_new)
        if not df_new.empty:
            logging.debug(f"成功获取并过滤后保留 {len(df_new)} 条 {ts_code} 的 Stock_Daily 数据")
        else:
            logging.debug(f"代码 {ts_code} 的所有数据都被过滤掉了")
    else:
        logging.debug(f"代码 {ts_code} 在范围 {start_date}-{end_date} 内没有获取到新的 Stock_Daily 数据。")
    return df_new

def fetch_daily_basic_task(ts_code, start_date, end_date):
    """线程任务：获取单个股票每日基本面数据（PE、PB等）。"""
    logging.debug(f"线程任务：获取 Daily_Basic {ts_code} 从 {start_date} 到 {end_date}")
    if not ts_code or pd.isna(ts_code):
        logging.warning(f"股票代码缺失或无效 ({ts_code})，跳过下载 Daily_Basic。")
        return pd.DataFrame()

    df_new = fetch_with_retry(pro.daily_basic,
                              ts_code=ts_code,
                              start_date=start_date,
                              end_date=end_date)
    if not df_new.empty:
        logging.debug(f"成功获取 {len(df_new)} 条 {ts_code} 的 Daily_Basic 数据")
    else:
        logging.debug(f"代码 {ts_code} 在范围 {start_date}-{end_date} 内没有获取到新的 Daily_Basic 数据。")
    return df_new

# --- 主执行逻辑 ---
if __name__ == "__main__":
    main_start_time = time.time()
    logging.info("--- 开始执行主板股票数据下载脚本（覆盖模式）---")
    logging.info(f"配置: MAX_WORKERS={MAX_WORKERS}, INTER_CALL_DELAY={INTER_CALL_DELAY}")
    logging.info(f"下载日期范围: {TARGET_START_DATE} 到 {TARGET_END_DATE}")
    logging.info(f"STOCK_DAILY 请求字段: {STOCK_DAILY_FIELDS}")
    logging.info("注意: 此脚本将覆盖现有数据文件，不进行增量更新")

    ensure_dir(DATA_DIR)

    try:
        target_start_dt = datetime.strptime(TARGET_START_DATE, '%Y%m%d')
        target_end_dt = datetime.strptime(TARGET_END_DATE, '%Y%m%d')
    except ValueError:
        logging.error("目标开始日期或结束日期格式错误，请使用 YYYYMMDD 格式。")
        exit()

    # 1. 获取主板股票基本信息并覆盖保存
    stock_info_df = get_stock_basic_info()
    if stock_info_df.empty:
        logging.error("未能获取主板股票基本信息，脚本终止。")
        exit()

    if 'ts_code' not in stock_info_df.columns:
         logging.error("主板股票基本信息文件中未找到 'ts_code' 列，脚本终止。")
         exit()

    # 获取所有主板股票代码
    all_stock_codes = stock_info_df['ts_code'].dropna().unique().tolist()
    logging.info(f"获取到 {len(all_stock_codes)} 个主板股票代码")

    # 2. 确定需要下载的股票日线数据范围（覆盖模式）
    logging.info("--- 确定需要下载的主板股票日线数据范围（覆盖模式）---")
    stock_daily_file = os.path.join(DATA_DIR, STOCK_DAILY_FILE)
    stock_daily_tasks = determine_fetch_ranges_per_code(stock_daily_file, 'ts_code', all_stock_codes, target_start_dt, target_end_dt, stock_info_df)
    total_stock_daily_tasks = len(stock_daily_tasks)

    # 3. 确定需要下载的每日基本面数据范围（覆盖模式）
    logging.info("--- 确定需要下载的每日基本面数据范围（覆盖模式）---")
    daily_basic_file = os.path.join(DATA_DIR, DAILY_BASIC_FILE)
    daily_basic_tasks = determine_fetch_ranges_per_code(daily_basic_file, 'ts_code', all_stock_codes, target_start_dt, target_end_dt, stock_info_df)
    total_daily_basic_tasks = len(daily_basic_tasks)

    # 4. 下载更新主板股票日线数据
    logging.info("--- 开始下载更新主板股票日线数据 ---")
    completed_stock_daily_tasks = 0
    all_new_stock_daily_data = []

    if total_stock_daily_tasks > 0:
        logging.info(f"需要下载 {total_stock_daily_tasks} 个股票日线任务。")
        phase1_start_time = time.time()
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = {executor.submit(fetch_stock_daily_task, code, start, end): (code) for code, start, end in stock_daily_tasks}
            for future in as_completed(futures):
                task_code = futures[future]
                try:
                    result_df = future.result()
                    if result_df is not None and not result_df.empty:
                        all_new_stock_daily_data.append(result_df)
                    completed_stock_daily_tasks += 1
                    if completed_stock_daily_tasks % 50 == 0 or completed_stock_daily_tasks == total_stock_daily_tasks:
                        logging.info(f"[股票日线阶段] 下载进度: {completed_stock_daily_tasks}/{total_stock_daily_tasks} 个任务已完成。")
                except Exception as e:
                    logging.error(f"[股票日线阶段] 处理任务 ({task_code}) 结果时发生错误: {e}")
                    completed_stock_daily_tasks += 1
        phase1_end_time = time.time()
        logging.info(f"[股票日线阶段] 所有下载任务完成，耗时: {phase1_end_time - phase1_start_time:.2f} 秒。")

        if all_new_stock_daily_data:
            logging.info("[股票日线阶段] 开始合并和保存数据...")
            valid_stock_daily_data = [df for df in all_new_stock_daily_data if df is not None and not df.empty and not df.isna().all(axis=None)]
            if valid_stock_daily_data:
                combined_stock_daily_df = pd.concat(valid_stock_daily_data, ignore_index=True)
                logging.info(f"[股票日线阶段] 合并后共有 {len(combined_stock_daily_df)} 行数据准备保存")
                # 覆盖保存数据
                save_data_overwrite(combined_stock_daily_df, stock_daily_file)
            else:
                logging.info("[股票日线阶段] 没有有效的股票日线数据需要保存。")
        else:
            logging.info("[股票日线阶段] 没有新的股票日线数据需要保存。")
    else:
        logging.info("[股票日线阶段] 没有需要执行的股票日线下载任务。")

    logging.info("--- 结束股票日线数据处理 ---")

    # 5. 下载更新每日基本面数据
    logging.info("--- 开始下载更新每日基本面数据 ---")
    completed_daily_basic_tasks = 0
    all_new_daily_basic_data = []

    if total_daily_basic_tasks > 0:
        logging.info(f"需要下载 {total_daily_basic_tasks} 个每日基本面任务。")
        phase2_start_time = time.time()
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            futures = {executor.submit(fetch_daily_basic_task, code, start, end): (code) for code, start, end in daily_basic_tasks}
            for future in as_completed(futures):
                task_code = futures[future]
                try:
                    result_df = future.result()
                    if result_df is not None and not result_df.empty:
                        all_new_daily_basic_data.append(result_df)
                    completed_daily_basic_tasks += 1
                    if completed_daily_basic_tasks % 50 == 0 or completed_daily_basic_tasks == total_daily_basic_tasks:
                        logging.info(f"[每日基本面阶段] 下载进度: {completed_daily_basic_tasks}/{total_daily_basic_tasks} 个任务已完成。")
                except Exception as e:
                    logging.error(f"[每日基本面阶段] 处理任务 ({task_code}) 结果时发生错误: {e}")
                    completed_daily_basic_tasks += 1
        phase2_end_time = time.time()
        logging.info(f"[每日基本面阶段] 所有下载任务完成，耗时: {phase2_end_time - phase2_start_time:.2f} 秒。")

        if all_new_daily_basic_data:
            logging.info("[每日基本面阶段] 开始合并和保存数据...")
            valid_daily_basic_data = [df for df in all_new_daily_basic_data if df is not None and not df.empty and not df.isna().all(axis=None)]
            if valid_daily_basic_data:
                combined_daily_basic_df = pd.concat(valid_daily_basic_data, ignore_index=True)
                logging.info(f"[每日基本面阶段] 合并后共有 {len(combined_daily_basic_df)} 行数据准备保存")
                # 覆盖保存数据
                save_data_overwrite(combined_daily_basic_df, daily_basic_file)
            else:
                logging.info("[每日基本面阶段] 没有有效的每日基本面数据需要保存。")
        else:
            logging.info("[每日基本面阶段] 没有新的每日基本面数据需要保存。")
    else:
        logging.info("[每日基本面阶段] 没有需要执行的每日基本面下载任务。")

    logging.info("--- 结束每日基本面数据处理 ---")

    # 不再下载财务数据
    logging.info("--- 跳过财务数据下载 ---")

    # 7. 总结处理结果
    logging.info("=== 股票数据处理总结 ===")
    logging.info(f"- 总共处理了 {len(stock_info_df)} 条创业板股票基本信息")
    logging.info(f"- 确定了 {total_stock_daily_tasks} 个股票日线下载任务")
    logging.info(f"- 确定了 {total_daily_basic_tasks} 个每日基本面下载任务")

    if os.path.exists(stock_daily_file):
        try:
            stock_daily_count = len(pd.read_csv(stock_daily_file))
            logging.info(f"- 股票日线数据现有 {stock_daily_count} 行")
        except Exception as e:
            logging.error(f"读取股票日线数据文件失败: {e}")

    if os.path.exists(daily_basic_file):
        try:
            daily_basic_count = len(pd.read_csv(daily_basic_file))
            logging.info(f"- 每日基本面数据现有 {daily_basic_count} 行")
        except Exception as e:
            logging.error(f"读取每日基本面数据文件失败: {e}")

    main_end_time = time.time()

    logging.info(f"--- 创业板股票数据下载脚本执行完毕，总耗时: {main_end_time - main_start_time:.2f} 秒 ---")
