#!/usr/bin/env python3
"""
测试新的损失函数
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入损失函数
from train import TrendCaptureLoss, RankingLoss, CombinedLoss

def test_loss_functions():
    """测试不同损失函数的行为"""
    
    # 创建测试数据
    # 场景1: 真实上涨，预测也上涨 (理想情况)
    targets_up = torch.tensor([2.0, 3.5, 1.8, 4.2])
    preds_up = torch.tensor([1.8, 3.2, 1.5, 4.0])
    
    # 场景2: 真实上涨，预测下跌 (错误预测)
    targets_up2 = torch.tensor([2.0, 3.5, 1.8, 4.2])
    preds_down = torch.tensor([-0.5, -1.2, -0.8, -1.5])
    
    # 场景3: 真实下跌，预测上涨 (反弹误判)
    targets_down = torch.tensor([-1.5, -2.3, -0.8, -3.1])
    preds_up2 = torch.tensor([1.2, 2.1, 0.9, 2.8])
    
    # 场景4: 真实下跌，预测也下跌 (正确预测下跌)
    targets_down2 = torch.tensor([-1.5, -2.3, -0.8, -3.1])
    preds_down2 = torch.tensor([-1.2, -2.0, -0.6, -2.8])
    
    # 初始化损失函数
    mse_loss = torch.nn.MSELoss()
    trend_loss = TrendCaptureLoss(alpha=2.0, beta=1.5, gamma=3.0, trend_threshold=0.5)
    ranking_loss = RankingLoss()
    combined_loss = CombinedLoss()
    
    print("=" * 60)
    print("损失函数测试结果")
    print("=" * 60)
    
    scenarios = [
        ("真实上涨，预测上涨", targets_up, preds_up),
        ("真实上涨，预测下跌", targets_up2, preds_down),
        ("真实下跌，预测上涨(反弹误判)", targets_down, preds_up2),
        ("真实下跌，预测下跌", targets_down2, preds_down2)
    ]
    
    for scenario_name, targets, preds in scenarios:
        print(f"\n场景: {scenario_name}")
        print(f"目标值: {targets.numpy()}")
        print(f"预测值: {preds.numpy()}")
        
        mse_val = mse_loss(preds, targets).item()
        trend_val = trend_loss(preds, targets).item()
        ranking_val = ranking_loss(preds, targets).item()
        combined_val = combined_loss(preds, targets).item()
        
        print(f"MSE损失:      {mse_val:.4f}")
        print(f"趋势捕捉损失: {trend_val:.4f}")
        print(f"排序损失:     {ranking_val:.4f}")
        print(f"组合损失:     {combined_val:.4f}")
        print("-" * 40)

def visualize_loss_behavior():
    """可视化损失函数的行为"""
    
    # 创建一个固定的目标值（上涨2%）
    target = torch.tensor([2.0])
    
    # 创建一系列预测值，从-5到5
    pred_range = torch.linspace(-5, 5, 100)
    
    # 计算不同损失函数的值
    mse_loss = torch.nn.MSELoss()
    trend_loss = TrendCaptureLoss(alpha=2.0, beta=1.5, gamma=3.0, trend_threshold=0.5)
    
    mse_values = []
    trend_values = []
    
    for pred in pred_range:
        pred_tensor = torch.tensor([pred.item()])
        
        mse_val = mse_loss(pred_tensor, target).item()
        trend_val = trend_loss(pred_tensor, target).item()
        
        mse_values.append(mse_val)
        trend_values.append(trend_val)
    
    # 绘制图形
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(pred_range.numpy(), mse_values, 'b-', label='MSE Loss', linewidth=2)
    plt.axvline(x=2.0, color='r', linestyle='--', alpha=0.7, label='True Value')
    plt.axvline(x=0, color='gray', linestyle=':', alpha=0.5)
    plt.xlabel('预测值')
    plt.ylabel('损失值')
    plt.title('MSE损失函数 (目标值=2.0)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 1, 2)
    plt.plot(pred_range.numpy(), trend_values, 'g-', label='Trend Capture Loss', linewidth=2)
    plt.axvline(x=2.0, color='r', linestyle='--', alpha=0.7, label='True Value')
    plt.axvline(x=0, color='gray', linestyle=':', alpha=0.5)
    plt.axvline(x=0.5, color='orange', linestyle=':', alpha=0.7, label='Trend Threshold')
    plt.xlabel('预测值')
    plt.ylabel('损失值')
    plt.title('趋势捕捉损失函数 (目标值=2.0)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('loss_function_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("损失函数对比图已保存为 'loss_function_comparison.png'")

def test_gradient_behavior():
    """测试梯度行为"""
    print("\n" + "=" * 60)
    print("梯度行为测试")
    print("=" * 60)
    
    # 创建需要梯度的预测值
    pred = torch.tensor([1.0], requires_grad=True)
    target_up = torch.tensor([2.0])  # 上涨目标
    target_down = torch.tensor([-1.0])  # 下跌目标
    
    # 测试不同损失函数的梯度
    losses = {
        'MSE': torch.nn.MSELoss(),
        'TrendCapture': TrendCaptureLoss(alpha=2.0, beta=1.5, gamma=3.0)
    }
    
    for loss_name, loss_fn in losses.items():
        print(f"\n{loss_name} 损失函数梯度:")
        
        # 对上涨目标的梯度
        pred.grad = None
        loss_up = loss_fn(pred, target_up)
        loss_up.backward()
        grad_up = pred.grad.item()
        
        # 对下跌目标的梯度
        pred.grad = None
        loss_down = loss_fn(pred, target_down)
        loss_down.backward()
        grad_down = pred.grad.item()
        
        print(f"  预测值=1.0, 目标上涨(2.0): 梯度={grad_up:.4f}")
        print(f"  预测值=1.0, 目标下跌(-1.0): 梯度={grad_down:.4f}")

if __name__ == "__main__":
    print("开始测试损失函数...")
    
    # 基本功能测试
    test_loss_functions()
    
    # 梯度行为测试
    test_gradient_behavior()
    
    # 可视化测试
    try:
        visualize_loss_behavior()
    except Exception as e:
        print(f"可视化测试跳过 (需要matplotlib): {e}")
    
    print("\n测试完成!")
